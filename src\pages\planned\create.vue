<script setup lang="ts">
import { usePlannedCapacityCreateForm } from './schema'
import { capacityApi } from '~/api/capacity'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit, values } = usePlannedCapacityCreateForm()

const save = handleSubmit(async (formValues) => {
  try {
    loading.value = true
    await capacityApi.create(formValues)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

// 计算属性，用于动态生成提示信息
const hourRange = computed(() => {
  const startHour = values.hourOfDay
  if (startHour === undefined || startHour === null || startHour < 0 || startHour > 23)
    return ''

  const endHour = (startHour + 1) % 24
  return `时间范围 ${startHour}:00-${endHour}:00`
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建计划产能" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LDictSelect name="lineId" label="线体编码" code="LINE_CODE" />
        <LDatePicker name="workDate" label="生产日期" />
        <div>
          <LInputNumber name="hourOfDay" label="时间" :input-props="{ showButtons: true, min: 0, max: 23 }" />
          <!-- 添加的小提示 -->
          <small v-if="hourRange" class="mt-1 block text-gray-500">
            {{ hourRange }}
          </small>
        </div>
        <LInputNumber name="plannedQuantity" label="计划产能" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
