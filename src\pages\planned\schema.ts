import { toTypedSchema } from '@vee-validate/zod'
import { formatDate } from '@vueuse/core'

// 查询理论值
const PlannedCapacitySearchSchema = toTypedSchema(z.object({
  lineId: z.string().optional(),
  workDate: z.date().transform(value => formatDate(value, 'YYYY-MM-DD')).optional(),
}))

// 创建理论值
const PlannedCapacityCreateSchema = toTypedSchema(z.object({
  lineId: z.string({
    required_error: '请输入线体编码',
  }),
  workDate: z.date({
    required_error: '请输入生产日期',
  }).transform(value => formatDate(value, 'YYYY-MM-DD')),
  hourOfDay: z.number({
    required_error: '请输入时间',
  }),
  plannedQuantity: z.number({
    required_error: '请输入计划产能',
  }),
}))

const PlannedCapacityEditSchema = toTypedSchema(z.object({
  lineId: z.string({
    required_error: '请输入线体编码',
  }),
  workDate: z.string({
    required_error: '请输入生产日期',
  }),
  hourOfDay: z.number({
    required_error: '请输入时间',
  }),
  plannedQuantity: z.number({
    required_error: '请输入计划产能',
  }),
}))

export function usePlannedCapacitySearchForm() {
  const form = useForm({
    validationSchema: PlannedCapacitySearchSchema,
  })
  return form
}

export function usePlannedCapacityCreateForm() {
  const form = useForm({
    validationSchema: PlannedCapacityCreateSchema,
  })
  return form
}

export function usePlannedCapacityEditForm() {
  const form = useForm({
    validationSchema: PlannedCapacityEditSchema,
  })
  return form
}
