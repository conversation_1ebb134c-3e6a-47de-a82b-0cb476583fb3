export interface CapacityReportDto {
  startWorkTime: string
  endWorkTime: string
  workDate: string
  hour: number
  plannedQuantity: number
  actualQuantity: number
  achievementRate: string
  theoreticalQuantity: string
  status: '达标' | '未达标'
  reason: string | null
}

export interface LineSummary {
  lineCode: string
  totalPlannedQuantity: number
  totalActualQuantity: number
  totalTheoreticalQuality: number
  totalAchievementRate: number
  metCount: number
  notMetCount: number
}

export interface PlannedCapacityInfo {
  track: string
  trackName: string
  lineCode: string
  productModel: string
  plannedQuantity: number
}

export interface CapacityReportWithLine {
  lineCode: string
  capacityReportDtos: CapacityReportDto[]
  lineSummary: LineSummary
  plannedCapacityInfos: PlannedCapacityInfo[]
}

export interface PlannedCapacity {
  id: string
  lineId: string
  workDate: string
  hourOfDay: number
  plannedQuantity: number
}

export interface PlannedCapacityCreate {
  lineId: string
  workDate: string
  hourOfDay: number
  plannedQuantity: number
}

export interface PlannedCapacityEdit {
  lineId: string
  workDate: string
  hourOfDay: number
  plannedQuantity: number
}

export interface PlannedCapacitySearch {
  lineId?: string
  workDate?: string
}
